import request from '@/utils/request'

export function getAllList(query) {
  return request({
    url: '/wechat/company/getAllList',
    method: 'get',
    params: query
  })
}
// 查询主管部门列表
export function getDepartmentList(query) {
  return request({
    url: '/wechat/company/list',
    method: 'get',
    params: query
  })
}

// 查询主管部门详细
export function getDepartment(departmentId) {
  return request({
    url: '/wechat/company/' + departmentId,
    method: 'get'
  })
}

// 新增主管部门
export function addDepartment(data) {
  return request({
    url: '/wechat/company',
    method: 'post',
    data: data
  })
}

// 修改主管部门
export function updateDepartment(data) {
  return request({
    url: '/wechat/company',
    method: 'put',
    data: data
  })
}

// 删除主管部门
export function delDepartment(departmentId) {
  return request({
    url: '/wechat/company/' + departmentId,
    method: 'delete'
  })
}
